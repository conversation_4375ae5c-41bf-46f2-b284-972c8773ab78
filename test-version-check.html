<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Version Check Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        #log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>T<PERSON> AI - Version Check Test</h1>
    
    <div class="test-section">
        <h2>Version Manager Test</h2>
        <p>This page tests the version checking functionality of the userscript.</p>
        
        <div>
            <strong>Local Version:</strong> <span id="local-version">3.1</span><br>
            <strong>Remote URL:</strong> <span id="remote-url">https://api.jsonstorage.net/v1/json/d206ce58-9543-48db-a5e4-997cfc745ef3/7e7adc93-d373-4050-b5c1-c8b7115fbdb3?apiKey=796c9bbf-df23-4228-afef-c3357694c29b</span>
        </div>
        
        <div style="margin: 20px 0;">
            <button onclick="testVersionCheck()">Test Version Check</button>
            <button onclick="testVersionComparison()">Test Version Comparison</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>
    
    <div class="test-section">
        <h3>Log Output</h3>
        <div id="log"></div>
    </div>

    <script>
        // Copy the VersionManager code from the userscript for testing
        const LOCAL_SCRIPT_VERSION = "3.1";
        const REMOTE_CONFIG_URL = "https://api.jsonstorage.net/v1/json/d206ce58-9543-48db-a5e4-997cfc745ef3/7e7adc93-d373-4050-b5c1-c8b7115fbdb3?apiKey=796c9bbf-df23-4228-afef-c3357694c29b";
        
        var VersionManager = {
            isScriptBlocked: false,
            lastCheckTime: 0,
            checkInterval: 30 * 60 * 1000,
            
            log: function(message, level) {
                level = level || 'INFO';
                var timestamp = new Date().toLocaleTimeString();
                var color = level === 'ERROR' ? 'red' :
                           level === 'WARN' ? 'orange' :
                           level === 'SUCCESS' ? 'green' : 'blue';
                
                var logDiv = document.getElementById('log');
                var logEntry = document.createElement('div');
                logEntry.style.color = color;
                logEntry.innerHTML = '[VersionManager] ' + timestamp + ' [' + level + ']: ' + message;
                logDiv.appendChild(logEntry);
                logDiv.scrollTop = logDiv.scrollHeight;
                
                console.log('%c[VersionManager] ' + timestamp + ' [' + level + ']: ' + message, 'color: ' + color);
            },
            
            performVersionCheck: function() {
                var self = this;
                this.log('Performing version check...');
                
                fetch(REMOTE_CONFIG_URL)
                    .then(function(response) {
                        if (!response.ok) {
                            throw new Error('Network response was not ok: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(function(data) {
                        self.handleRemoteConfig(data);
                    })
                    .catch(function(error) {
                        self.handleVersionCheckError(error);
                    });
            },
            
            handleRemoteConfig: function(config) {
                this.log('Remote config received: ' + JSON.stringify(config));
                
                if (config.paid !== true) {
                    this.log('Script access denied - paid status is false', 'ERROR');
                    this.showNotification('This script requires a valid license. Please contact the developer.', 'error');
                    return;
                }
                
                if (config.scriptVersion && this.isNewerVersion(config.scriptVersion, LOCAL_SCRIPT_VERSION)) {
                    this.log('Newer version available: ' + config.scriptVersion + ' (current: ' + LOCAL_SCRIPT_VERSION + ')', 'WARN');
                    this.showNotification('A newer version (' + config.scriptVersion + ') is available. Please update your script.', 'warning');
                    return;
                }
                
                this.log('Version check passed - script is up to date', 'SUCCESS');
                this.lastCheckTime = Date.now();
            },
            
            handleVersionCheckError: function(error) {
                this.log('Version check failed: ' + error.message, 'ERROR');
                this.showNotification('Version check failed: ' + error.message + '. Script will continue running.', 'warning');
            },
            
            isNewerVersion: function(remoteVersion, localVersion) {
                var remoteParts = remoteVersion.split('.').map(Number);
                var localParts = localVersion.split('.').map(Number);
                
                for (var i = 0; i < Math.max(remoteParts.length, localParts.length); i++) {
                    var remote = remoteParts[i] || 0;
                    var local = localParts[i] || 0;
                    
                    if (remote > local) return true;
                    if (remote < local) return false;
                }
                
                return false;
            },
            
            showNotification: function(message, type) {
                var notification = document.createElement('div');
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = type === 'warning' ? '#f39c12' : '#e74c3c';
                notification.style.color = 'white';
                notification.style.padding = '15px';
                notification.style.borderRadius = '5px';
                notification.style.zIndex = '10000';
                notification.style.maxWidth = '300px';
                notification.style.fontSize = '14px';
                notification.innerHTML = message;
                
                document.body.appendChild(notification);
                
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 10000);
            }
        };
        
        function testVersionCheck() {
            VersionManager.log('Starting version check test...', 'INFO');
            VersionManager.performVersionCheck();
        }
        
        function testVersionComparison() {
            VersionManager.log('Testing version comparison logic...', 'INFO');
            
            var tests = [
                { remote: '3.2', local: '3.1', expected: true },
                { remote: '3.1', local: '3.1', expected: false },
                { remote: '3.0', local: '3.1', expected: false },
                { remote: '4.0', local: '3.1', expected: true },
                { remote: '3.1.1', local: '3.1', expected: true },
                { remote: '3.1', local: '3.1.1', expected: false }
            ];
            
            tests.forEach(function(test) {
                var result = VersionManager.isNewerVersion(test.remote, test.local);
                var status = result === test.expected ? 'SUCCESS' : 'ERROR';
                VersionManager.log('Version test: ' + test.remote + ' > ' + test.local + ' = ' + result + ' (expected: ' + test.expected + ')', status);
            });
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            VersionManager.log('Version Manager test page loaded', 'INFO');
            VersionManager.log('Local version: ' + LOCAL_SCRIPT_VERSION, 'INFO');
        });
    </script>
</body>
</html>
